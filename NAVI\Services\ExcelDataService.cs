using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace NAVI.Services
{
    /// <summary>
    /// Excel数据服务类
    /// </summary>
    public class ExcelDataService
    {
        private readonly string _filePath;
        private IWorkbook _workbook;

        public ExcelDataService(string filePath)
        {
            _filePath = filePath;
            LoadWorkbook();
        }

        /// <summary>
        /// 加载工作簿
        /// </summary>
        private void LoadWorkbook()
        {
            try
            {
                using (var fileStream = new FileStream(_filePath, FileMode.Open, FileAccess.Read))
                {
                    if (_filePath.EndsWith(".xlsx"))
                    {
                        _workbook = new XSSFWorkbook(fileStream);
                    }
                    else if (_filePath.EndsWith(".xls"))
                    {
                        _workbook = new HSSFWorkbook(fileStream);
                    }
                    else
                    {
                        throw new ArgumentException("不支持的文件格式");
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"加载Excel文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取工作表数据
        /// </summary>
        /// <param name="sheetName">工作表名称</param>
        /// <returns>数据字典列表</returns>
        public List<Dictionary<string, object>> GetSheetData(string sheetName)
        {
            var result = new List<Dictionary<string, object>>();

            try
            {
                var sheet = _workbook.GetSheet(sheetName);
                if (sheet == null)
                {
                    throw new ArgumentException($"找不到工作表: {sheetName}");
                }

                // 获取表头
                var headerRow = sheet.GetRow(0);
                if (headerRow == null) return result;

                var headers = new List<string>();
                var validColumnIndexes = new List<int>();

                for (int i = 0; i < headerRow.LastCellNum; i++)
                {
                    var cell = headerRow.GetCell(i);
                    var headerValue = GetCellDisplayValue(cell);

                    // 只添加非空的列头
                    if (!string.IsNullOrWhiteSpace(headerValue))
                    {
                        headers.Add(headerValue);
                        validColumnIndexes.Add(i);
                    }
                }

                // 读取数据行
                for (int rowIndex = 1; rowIndex <= sheet.LastRowNum; rowIndex++)
                {
                    var row = sheet.GetRow(rowIndex);
                    if (row == null) continue;

                    var rowData = new Dictionary<string, object>();
                    bool hasData = false;

                    for (int i = 0; i < headers.Count; i++)
                    {
                        var colIndex = validColumnIndexes[i];
                        var cell = row.GetCell(colIndex);
                        var value = GetCellDisplayValue(cell);

                        rowData[headers[i]] = value;

                        // 检查是否有非空数据
                        if (!string.IsNullOrWhiteSpace(value?.ToString()))
                        {
                            hasData = true;
                        }
                    }

                    // 只添加有数据的行
                    if (hasData)
                    {
                        result.Add(rowData);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"读取工作表数据失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 获取工作表列名
        /// </summary>
        /// <param name="sheetName">工作表名称</param>
        /// <returns>列名列表</returns>
        public List<string> GetSheetColumns(string sheetName)
        {
            var columns = new List<string>();

            try
            {
                var sheet = _workbook.GetSheet(sheetName);
                if (sheet == null) return columns;

                var headerRow = sheet.GetRow(0);
                if (headerRow == null) return columns;

                for (int i = 0; i < headerRow.LastCellNum; i++)
                {
                    var cell = headerRow.GetCell(i);
                    var headerValue = GetCellDisplayValue(cell);

                    // 只添加非空的列头
                    if (!string.IsNullOrWhiteSpace(headerValue))
                    {
                        columns.Add(headerValue);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"读取工作表列名失败: {ex.Message}");
            }

            return columns;
        }

        /// <summary>
        /// 更新行数据
        /// </summary>
        /// <param name="sheetName">工作表名称</param>
        /// <param name="rowIndex">行索引（从1开始，0是表头）</param>
        /// <param name="data">数据字典</param>
        public void UpdateRow(string sheetName, int rowIndex, Dictionary<string, object> data)
        {
            try
            {
                var sheet = _workbook.GetSheet(sheetName);
                if (sheet == null) throw new ArgumentException($"找不到工作表: {sheetName}");

                var headerRow = sheet.GetRow(0);
                if (headerRow == null) throw new Exception("找不到表头行");

                // 获取列索引映射
                var columnIndexMap = new Dictionary<string, int>();
                for (int i = 0; i < headerRow.LastCellNum; i++)
                {
                    var cell = headerRow.GetCell(i);
                    var columnName = GetCellDisplayValue(cell);
                    if (!string.IsNullOrEmpty(columnName))
                    {
                        columnIndexMap[columnName] = i;
                    }
                }

                // 获取或创建数据行
                var row = sheet.GetRow(rowIndex) ?? sheet.CreateRow(rowIndex);

                // 更新数据
                foreach (var kvp in data)
                {
                    if (columnIndexMap.ContainsKey(kvp.Key))
                    {
                        var colIndex = columnIndexMap[kvp.Key];
                        var cell = row.GetCell(colIndex) ?? row.CreateCell(colIndex);

                        // 检查原单元格是否有公式
                        var originalCell = row.GetCell(colIndex);
                        if (originalCell != null && originalCell.CellType == CellType.Formula)
                        {
                            // 如果原来是公式单元格，保持公式不变（除非明确要修改）
                            // 这里可以根据业务需求决定是否允许修改公式单元格
                            continue;
                        }

                        SetCellValueSafely(cell, kvp.Value);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"更新行数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加新行（复制最后一行的格式）
        /// </summary>
        /// <param name="sheetName">工作表名称</param>
        /// <param name="data">数据字典</param>
        public void AddRow(string sheetName, Dictionary<string, object> data)
        {
            try
            {
                var sheet = _workbook.GetSheet(sheetName);
                if (sheet == null) throw new ArgumentException($"找不到工作表: {sheetName}");

                var newRowIndex = sheet.LastRowNum + 1;

                // 复制最后一行的格式（如果存在数据行）
                if (sheet.LastRowNum > 0)
                {
                    var templateRow = sheet.GetRow(sheet.LastRowNum);
                    if (templateRow != null)
                    {
                        CopyRowFormat(sheet, templateRow, newRowIndex);
                    }
                }

                UpdateRow(sheetName, newRowIndex, data);
            }
            catch (Exception ex)
            {
                throw new Exception($"添加新行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 复制行格式
        /// </summary>
        private void CopyRowFormat(ISheet sheet, IRow templateRow, int newRowIndex)
        {
            var newRow = sheet.CreateRow(newRowIndex);
            newRow.Height = templateRow.Height;

            for (int i = 0; i < templateRow.LastCellNum; i++)
            {
                var templateCell = templateRow.GetCell(i);
                if (templateCell != null)
                {
                    var newCell = newRow.CreateCell(i);

                    // 复制单元格样式
                    newCell.CellStyle = templateCell.CellStyle;

                    // 如果模板单元格有公式，复制公式
                    if (templateCell.CellType == CellType.Formula)
                    {
                        try
                        {
                            // 调整公式中的行引用
                            var formula = templateCell.CellFormula;
                            var adjustedFormula = AdjustFormulaForNewRow(formula, templateRow.RowNum, newRowIndex);
                            newCell.SetCellFormula(adjustedFormula);
                        }
                        catch
                        {
                            // 如果公式调整失败，设置为空值
                            newCell.SetCellValue(string.Empty);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 调整公式中的行引用
        /// </summary>
        private string AdjustFormulaForNewRow(string formula, int oldRowNum, int newRowNum)
        {
            // 简单的行号替换（可以根据需要扩展更复杂的公式处理）
            var oldRowRef = (oldRowNum + 1).ToString(); // Excel行号从1开始
            var newRowRef = (newRowNum + 1).ToString();

            return formula.Replace(oldRowRef, newRowRef);
        }

        /// <summary>
        /// 删除行（安全删除，保持数据结构）
        /// </summary>
        /// <param name="sheetName">工作表名称</param>
        /// <param name="rowIndex">行索引（从1开始）</param>
        public void DeleteRow(string sheetName, int rowIndex)
        {
            try
            {
                var sheet = _workbook.GetSheet(sheetName);
                if (sheet == null) throw new ArgumentException($"找不到工作表: {sheetName}");

                var row = sheet.GetRow(rowIndex);
                if (row != null)
                {
                    // 获取列头信息以保持数据结构
                    var headerRow = sheet.GetRow(0);
                    var columnCount = headerRow?.LastCellNum ?? 0;

                    // 清空行数据而不是删除行，保持Excel结构
                    for (int i = 0; i < columnCount; i++)
                    {
                        var cell = row.GetCell(i);
                        if (cell != null)
                        {
                            // 如果是公式单元格，保留公式但清空值
                            if (cell.CellType == CellType.Formula)
                            {
                                try
                                {
                                    // 保持公式，但让其计算为空值
                                    // 这里可以根据具体需求调整
                                    cell.SetCellValue(string.Empty);
                                }
                                catch
                                {
                                    // 如果无法处理公式，直接清空
                                    row.RemoveCell(cell);
                                }
                            }
                            else
                            {
                                // 非公式单元格直接清空
                                cell.SetCellValue(string.Empty);
                            }
                        }
                    }

                    // 如果这是最后一行且不是唯一的数据行，可以安全删除
                    if (rowIndex == sheet.LastRowNum && sheet.LastRowNum > 1)
                    {
                        sheet.RemoveRow(row);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"删除行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存文件
        /// </summary>
        public void Save()
        {
            try
            {
                using (var fileStream = new FileStream(_filePath, FileMode.Create, FileAccess.Write))
                {
                    _workbook.Write(fileStream);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"保存文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取单元格显示值（避免公式，获取计算结果）
        /// </summary>
        private string GetCellDisplayValue(ICell cell)
        {
            if (cell == null) return string.Empty;

            try
            {
                switch (cell.CellType)
                {
                    case CellType.String:
                        return cell.StringCellValue ?? string.Empty;
                    case CellType.Numeric:
                        if (DateUtil.IsCellDateFormatted(cell))
                        {
                            return cell.DateCellValue.ToString("yyyy/MM/dd");
                        }
                        return cell.NumericCellValue.ToString();
                    case CellType.Boolean:
                        return cell.BooleanCellValue.ToString();
                    case CellType.Formula:
                        // 对于公式单元格，获取计算后的值而不是公式本身
                        try
                        {
                            var evaluator = _workbook.GetCreationHelper().CreateFormulaEvaluator();
                            var cellValue = evaluator.Evaluate(cell);

                            switch (cellValue.CellType)
                            {
                                case CellType.String:
                                    return cellValue.StringValue ?? string.Empty;
                                case CellType.Numeric:
                                    if (DateUtil.IsCellDateFormatted(cell))
                                    {
                                        return DateUtil.GetJavaDate(cellValue.NumberValue).ToString("yyyy/MM/dd");
                                    }
                                    return cellValue.NumberValue.ToString();
                                case CellType.Boolean:
                                    return cellValue.BooleanValue.ToString();
                                default:
                                    return string.Empty;
                            }
                        }
                        catch
                        {
                            // 如果公式计算失败，返回空字符串
                            return string.Empty;
                        }
                    case CellType.Blank:
                        return string.Empty;
                    default:
                        return cell.ToString() ?? string.Empty;
                }
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取单元格原始值（包括公式）
        /// </summary>
        private object GetCellValue(ICell cell)
        {
            if (cell == null) return string.Empty;

            switch (cell.CellType)
            {
                case CellType.String:
                    return cell.StringCellValue;
                case CellType.Numeric:
                    if (DateUtil.IsCellDateFormatted(cell))
                        return cell.DateCellValue;
                    return cell.NumericCellValue;
                case CellType.Boolean:
                    return cell.BooleanCellValue;
                case CellType.Formula:
                    return cell.CellFormula;
                default:
                    return string.Empty;
            }
        }

        /// <summary>
        /// 检查单元格是否包含公式
        /// </summary>
        private bool IsCellFormula(ICell cell)
        {
            return cell != null && cell.CellType == CellType.Formula;
        }

        /// <summary>
        /// 设置单元格值
        /// </summary>
        private void SetCellValue(ICell cell, object value)
        {
            if (value == null)
            {
                cell.SetCellValue(string.Empty);
                return;
            }

            switch (value)
            {
                case string str:
                    cell.SetCellValue(str);
                    break;
                case int intVal:
                    cell.SetCellValue(intVal);
                    break;
                case double doubleVal:
                    cell.SetCellValue(doubleVal);
                    break;
                case DateTime dateVal:
                    cell.SetCellValue(dateVal);
                    break;
                case bool boolVal:
                    cell.SetCellValue(boolVal);
                    break;
                default:
                    cell.SetCellValue(value.ToString());
                    break;
            }
        }

        /// <summary>
        /// 安全设置单元格值（保护公式单元格）
        /// </summary>
        private void SetCellValueSafely(ICell cell, object value)
        {
            try
            {
                // 如果是公式单元格，不修改
                if (cell.CellType == CellType.Formula)
                {
                    return;
                }

                SetCellValue(cell, value);
            }
            catch (Exception)
            {
                // 如果设置失败，尝试设置为字符串
                try
                {
                    cell.SetCellValue(value?.ToString() ?? string.Empty);
                }
                catch
                {
                    // 最后的保护，设置为空字符串
                    cell.SetCellValue(string.Empty);
                }
            }
        }

        /// <summary>
        /// 获取单元格详细信息（包括是否为公式）
        /// </summary>
        /// <param name="sheetName">工作表名称</param>
        /// <param name="rowIndex">行索引</param>
        /// <param name="columnName">列名</param>
        /// <returns>单元格信息</returns>
        public CellInfo GetCellInfo(string sheetName, int rowIndex, string columnName)
        {
            try
            {
                var sheet = _workbook.GetSheet(sheetName);
                if (sheet == null) return new CellInfo();

                var headerRow = sheet.GetRow(0);
                if (headerRow == null) return new CellInfo();

                // 找到列索引
                int colIndex = -1;
                for (int i = 0; i < headerRow.LastCellNum; i++)
                {
                    var cell = headerRow.GetCell(i);
                    if (GetCellDisplayValue(cell) == columnName)
                    {
                        colIndex = i;
                        break;
                    }
                }

                if (colIndex == -1) return new CellInfo();

                var row = sheet.GetRow(rowIndex);
                if (row == null) return new CellInfo();

                var targetCell = row.GetCell(colIndex);
                if (targetCell == null) return new CellInfo();

                return new CellInfo
                {
                    IsFormula = targetCell.CellType == CellType.Formula,
                    Formula = targetCell.CellType == CellType.Formula ? targetCell.CellFormula : null,
                    DisplayValue = GetCellDisplayValue(targetCell),
                    RawValue = GetCellValue(targetCell)
                };
            }
            catch
            {
                return new CellInfo();
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _workbook?.Close();
        }
    }

    /// <summary>
    /// 单元格信息
    /// </summary>
    public class CellInfo
    {
        public bool IsFormula { get; set; }
        public string Formula { get; set; }
        public string DisplayValue { get; set; }
        public object RawValue { get; set; }
    }
}
