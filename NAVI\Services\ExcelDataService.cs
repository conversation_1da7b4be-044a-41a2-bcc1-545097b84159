using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace NAVI.Services
{
    /// <summary>
    /// Excel数据服务类
    /// </summary>
    public class ExcelDataService
    {
        private readonly string _filePath;
        private IWorkbook _workbook;

        public ExcelDataService(string filePath)
        {
            _filePath = filePath;
            LoadWorkbook();
        }

        /// <summary>
        /// 加载工作簿
        /// </summary>
        private void LoadWorkbook()
        {
            try
            {
                using (var fileStream = new FileStream(_filePath, FileMode.Open, FileAccess.Read))
                {
                    if (_filePath.EndsWith(".xlsx"))
                    {
                        _workbook = new XSSFWorkbook(fileStream);
                    }
                    else if (_filePath.EndsWith(".xls"))
                    {
                        _workbook = new HSSFWorkbook(fileStream);
                    }
                    else
                    {
                        throw new ArgumentException("不支持的文件格式");
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"加载Excel文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取工作表数据
        /// </summary>
        /// <param name="sheetName">工作表名称</param>
        /// <returns>数据字典列表</returns>
        public List<Dictionary<string, object>> GetSheetData(string sheetName)
        {
            var result = new List<Dictionary<string, object>>();
            
            try
            {
                var sheet = _workbook.GetSheet(sheetName);
                if (sheet == null)
                {
                    throw new ArgumentException($"找不到工作表: {sheetName}");
                }

                // 获取表头
                var headerRow = sheet.GetRow(0);
                if (headerRow == null) return result;

                var headers = new List<string>();
                for (int i = 0; i < headerRow.LastCellNum; i++)
                {
                    var cell = headerRow.GetCell(i);
                    headers.Add(cell?.ToString() ?? $"Column{i}");
                }

                // 读取数据行
                for (int rowIndex = 1; rowIndex <= sheet.LastRowNum; rowIndex++)
                {
                    var row = sheet.GetRow(rowIndex);
                    if (row == null) continue;

                    var rowData = new Dictionary<string, object>();
                    for (int colIndex = 0; colIndex < headers.Count; colIndex++)
                    {
                        var cell = row.GetCell(colIndex);
                        var value = GetCellValue(cell);
                        rowData[headers[colIndex]] = value;
                    }
                    result.Add(rowData);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"读取工作表数据失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 获取工作表列名
        /// </summary>
        /// <param name="sheetName">工作表名称</param>
        /// <returns>列名列表</returns>
        public List<string> GetSheetColumns(string sheetName)
        {
            var columns = new List<string>();
            
            try
            {
                var sheet = _workbook.GetSheet(sheetName);
                if (sheet == null) return columns;

                var headerRow = sheet.GetRow(0);
                if (headerRow == null) return columns;

                for (int i = 0; i < headerRow.LastCellNum; i++)
                {
                    var cell = headerRow.GetCell(i);
                    columns.Add(cell?.ToString() ?? $"Column{i}");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"读取工作表列名失败: {ex.Message}");
            }

            return columns;
        }

        /// <summary>
        /// 更新行数据
        /// </summary>
        /// <param name="sheetName">工作表名称</param>
        /// <param name="rowIndex">行索引（从1开始，0是表头）</param>
        /// <param name="data">数据字典</param>
        public void UpdateRow(string sheetName, int rowIndex, Dictionary<string, object> data)
        {
            try
            {
                var sheet = _workbook.GetSheet(sheetName);
                if (sheet == null) throw new ArgumentException($"找不到工作表: {sheetName}");

                var headerRow = sheet.GetRow(0);
                if (headerRow == null) throw new Exception("找不到表头行");

                // 获取列索引映射
                var columnIndexMap = new Dictionary<string, int>();
                for (int i = 0; i < headerRow.LastCellNum; i++)
                {
                    var cell = headerRow.GetCell(i);
                    var columnName = cell?.ToString();
                    if (!string.IsNullOrEmpty(columnName))
                    {
                        columnIndexMap[columnName] = i;
                    }
                }

                // 获取或创建数据行
                var row = sheet.GetRow(rowIndex) ?? sheet.CreateRow(rowIndex);

                // 更新数据
                foreach (var kvp in data)
                {
                    if (columnIndexMap.ContainsKey(kvp.Key))
                    {
                        var colIndex = columnIndexMap[kvp.Key];
                        var cell = row.GetCell(colIndex) ?? row.CreateCell(colIndex);
                        SetCellValue(cell, kvp.Value);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"更新行数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加新行
        /// </summary>
        /// <param name="sheetName">工作表名称</param>
        /// <param name="data">数据字典</param>
        public void AddRow(string sheetName, Dictionary<string, object> data)
        {
            try
            {
                var sheet = _workbook.GetSheet(sheetName);
                if (sheet == null) throw new ArgumentException($"找不到工作表: {sheetName}");

                var newRowIndex = sheet.LastRowNum + 1;
                UpdateRow(sheetName, newRowIndex, data);
            }
            catch (Exception ex)
            {
                throw new Exception($"添加新行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除行
        /// </summary>
        /// <param name="sheetName">工作表名称</param>
        /// <param name="rowIndex">行索引（从1开始）</param>
        public void DeleteRow(string sheetName, int rowIndex)
        {
            try
            {
                var sheet = _workbook.GetSheet(sheetName);
                if (sheet == null) throw new ArgumentException($"找不到工作表: {sheetName}");

                var row = sheet.GetRow(rowIndex);
                if (row != null)
                {
                    sheet.RemoveRow(row);
                    
                    // 向上移动后续行
                    if (rowIndex < sheet.LastRowNum)
                    {
                        sheet.ShiftRows(rowIndex + 1, sheet.LastRowNum, -1);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"删除行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存文件
        /// </summary>
        public void Save()
        {
            try
            {
                using (var fileStream = new FileStream(_filePath, FileMode.Create, FileAccess.Write))
                {
                    _workbook.Write(fileStream);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"保存文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取单元格值
        /// </summary>
        private object GetCellValue(ICell cell)
        {
            if (cell == null) return string.Empty;

            switch (cell.CellType)
            {
                case CellType.String:
                    return cell.StringCellValue;
                case CellType.Numeric:
                    if (DateUtil.IsCellDateFormatted(cell))
                        return cell.DateCellValue;
                    return cell.NumericCellValue;
                case CellType.Boolean:
                    return cell.BooleanCellValue;
                case CellType.Formula:
                    return cell.CellFormula;
                default:
                    return string.Empty;
            }
        }

        /// <summary>
        /// 设置单元格值
        /// </summary>
        private void SetCellValue(ICell cell, object value)
        {
            if (value == null)
            {
                cell.SetCellValue(string.Empty);
                return;
            }

            switch (value)
            {
                case string str:
                    cell.SetCellValue(str);
                    break;
                case int intVal:
                    cell.SetCellValue(intVal);
                    break;
                case double doubleVal:
                    cell.SetCellValue(doubleVal);
                    break;
                case DateTime dateVal:
                    cell.SetCellValue(dateVal);
                    break;
                case bool boolVal:
                    cell.SetCellValue(boolVal);
                    break;
                default:
                    cell.SetCellValue(value.ToString());
                    break;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _workbook?.Close();
        }
    }
}
