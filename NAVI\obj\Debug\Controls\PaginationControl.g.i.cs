﻿#pragma checksum "..\..\..\Controls\PaginationControl.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "0D0959BD41666E287A3FA5E71FAAAB13B8CC5EE8592AF9276373D1CC72793045"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace NAVI.Controls {
    
    
    /// <summary>
    /// PaginationControl
    /// </summary>
    public partial class PaginationControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 79 "..\..\..\Controls\PaginationControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PageSizeComboBox;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\Controls\PaginationControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InfoTextBlock;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\Controls\PaginationControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox JumpPageTextBox;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\Controls\PaginationControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FirstPageButton;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\Controls\PaginationControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrevPageButton;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\Controls\PaginationControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PageNumbersPanel;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\Controls\PaginationControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NextPageButton;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\Controls\PaginationControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LastPageButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/NAVI;component/controls/paginationcontrol.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\Controls\PaginationControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PageSizeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 81 "..\..\..\Controls\PaginationControl.xaml"
            this.PageSizeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PageSizeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.InfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.JumpPageTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 106 "..\..\..\Controls\PaginationControl.xaml"
            this.JumpPageTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.JumpPageTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 111 "..\..\..\Controls\PaginationControl.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.JumpButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.FirstPageButton = ((System.Windows.Controls.Button)(target));
            
            #line 121 "..\..\..\Controls\PaginationControl.xaml"
            this.FirstPageButton.Click += new System.Windows.RoutedEventHandler(this.FirstPageButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.PrevPageButton = ((System.Windows.Controls.Button)(target));
            
            #line 126 "..\..\..\Controls\PaginationControl.xaml"
            this.PrevPageButton.Click += new System.Windows.RoutedEventHandler(this.PrevPageButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.PageNumbersPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 8:
            this.NextPageButton = ((System.Windows.Controls.Button)(target));
            
            #line 136 "..\..\..\Controls\PaginationControl.xaml"
            this.NextPageButton.Click += new System.Windows.RoutedEventHandler(this.NextPageButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.LastPageButton = ((System.Windows.Controls.Button)(target));
            
            #line 145 "..\..\..\Controls\PaginationControl.xaml"
            this.LastPageButton.Click += new System.Windows.RoutedEventHandler(this.LastPageButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

