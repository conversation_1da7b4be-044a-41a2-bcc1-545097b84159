using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using NAVI.Controls;
using NAVI.Models;
using NAVI.Services;
using NAVI.Windows;

namespace NAVI
{
    /// <summary>
    /// NationalDataControl.xaml 的交互逻辑
    /// </summary>
    public partial class NationalDataControl : UserControl
    {
        private ObservableCollection<NationalData> _nationalDataList;
        private List<NationalData> _allData;
        private List<string> _columnNames;
        private ExcelDataService _excelService;
        private const string SheetName = "国保联数据";
        private const string ExcelFilePath = "database\\shortstay_app_ver0.9.xlsx";

        // 分页相关
        private int _currentPage = 1;
        private int _pageSize = 10;
        private int _totalRecords = 0;

        public NationalDataControl()
        {
            InitializeComponent();
            InitializeData();
            SetupEventHandlers();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // 初始化Excel服务
                var fullPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, ExcelFilePath);
                if (File.Exists(fullPath))
                {
                    _excelService = new ExcelDataService(fullPath);
                    LoadDataFromExcel();
                }
                else
                {
                    // 如果Excel文件不存在，使用模拟数据
                    LoadSampleData();
                }

                // 设置搜索框的占位符效果
                SetupSearchBoxPlaceholder();
                
                // 初始化分页
                UpdatePagination();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化数据失败：{ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                LoadSampleData();
            }
        }

        /// <summary>
        /// 从Excel加载数据
        /// </summary>
        private void LoadDataFromExcel()
        {
            try
            {
                // 获取列名
                _columnNames = _excelService.GetSheetColumns(SheetName);
                
                // 获取数据
                var dictionaries = _excelService.GetSheetData(SheetName);
                _allData = NationalDataService.CreateFromDictionaries(dictionaries);
                
                // 创建动态列
                CreateDynamicColumns();
                
                // 应用分页
                ApplyPagination();
            }
            catch (Exception ex)
            {
                throw new Exception($"从Excel加载数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 加载模拟数据
        /// </summary>
        private void LoadSampleData()
        {
            _allData = NationalDataService.GetSampleData();
            if (_allData.Any())
            {
                _columnNames = _allData.First().PropertyNames.ToList();
                CreateDynamicColumns();
                ApplyPagination();
            }
        }

        /// <summary>
        /// 创建动态列
        /// </summary>
        private void CreateDynamicColumns()
        {
            // 清除除操作列外的所有列
            var operationColumn = NationalDataGrid.Columns.FirstOrDefault();
            NationalDataGrid.Columns.Clear();
            if (operationColumn != null)
            {
                NationalDataGrid.Columns.Add(operationColumn);
            }

            // 添加动态列
            foreach (var columnName in _columnNames)
            {
                var column = new DataGridTextColumn
                {
                    Header = columnName,
                    Binding = new System.Windows.Data.Binding($"[{columnName}]"),
                    Width = GetColumnWidth(columnName)
                };
                NationalDataGrid.Columns.Add(column);
            }
        }

        /// <summary>
        /// 获取列宽度
        /// </summary>
        private DataGridLength GetColumnWidth(string columnName)
        {
            // 根据列名设置合适的宽度
            if (columnName.Contains("序号") || columnName.Contains("番号"))
                return new DataGridLength(80);
            else if (columnName.Contains("氏名") || columnName.Contains("姓名"))
                return new DataGridLength(100);
            else if (columnName.Contains("住所") || columnName.Contains("地址"))
                return new DataGridLength(200);
            else if (columnName.Contains("电话") || columnName.Contains("联系"))
                return new DataGridLength(120);
            else if (columnName.Contains("日期") || columnName.Contains("年月日"))
                return new DataGridLength(100);
            else
                return new DataGridLength(120);
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // DataGrid选择变化事件
            NationalDataGrid.SelectionChanged += NationalDataGrid_SelectionChanged;
            
            // 搜索框事件
            SearchTextBox.GotFocus += SearchTextBox_GotFocus;
            SearchTextBox.LostFocus += SearchTextBox_LostFocus;
            SearchTextBox.TextChanged += SearchTextBox_TextChanged;
        }

        /// <summary>
        /// 设置搜索框占位符效果
        /// </summary>
        private void SetupSearchBoxPlaceholder()
        {
            SearchTextBox.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Gray);
        }

        /// <summary>
        /// 搜索框获得焦点事件
        /// </summary>
        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (SearchTextBox.Text == "输入保险者番号・被保险者番号・氏名・其他关键字")
            {
                SearchTextBox.Text = "";
                SearchTextBox.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Black);
            }
        }

        /// <summary>
        /// 搜索框失去焦点事件
        /// </summary>
        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                SearchTextBox.Text = "输入保险者番号・被保险者番号・氏名・其他关键字";
                SearchTextBox.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Gray);
            }
        }

        /// <summary>
        /// 搜索框文本变化事件
        /// </summary>
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            _currentPage = 1; // 重置到第一页
            ApplyPagination();
        }

        /// <summary>
        /// DataGrid选择变化事件
        /// </summary>
        private void NationalDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateStatusInfo();
        }

        /// <summary>
        /// 执行搜索过滤
        /// </summary>
        private List<NationalData> GetFilteredData()
        {
            string searchText = SearchTextBox.Text;
            
            // 如果是占位符文本或空白，返回所有数据
            if (string.IsNullOrWhiteSpace(searchText) || searchText == "输入保险者番号・被保险者番号・氏名・其他关键字")
            {
                return _allData;
            }

            // 执行搜索过滤
            return _allData.Where(item =>
            {
                foreach (var propertyName in item.PropertyNames)
                {
                    var value = item[propertyName]?.ToString() ?? "";
                    if (value.Contains(searchText))
                        return true;
                }
                return false;
            }).ToList();
        }

        /// <summary>
        /// 应用分页
        /// </summary>
        private void ApplyPagination()
        {
            var filteredData = GetFilteredData();
            _totalRecords = filteredData.Count;

            var pagedData = filteredData
                .Skip((_currentPage - 1) * _pageSize)
                .Take(_pageSize)
                .ToList();

            _nationalDataList = new ObservableCollection<NationalData>(pagedData);
            NationalDataGrid.ItemsSource = _nationalDataList;

            UpdatePagination();
            UpdateStatusInfo();
        }

        /// <summary>
        /// 更新分页信息
        /// </summary>
        private void UpdatePagination()
        {
            var totalPages = (int)Math.Ceiling((double)_totalRecords / _pageSize);
            
            PaginationControl.CurrentPage = _currentPage;
            PaginationControl.TotalPages = Math.Max(1, totalPages);
            PaginationControl.TotalRecords = _totalRecords;
            PaginationControl.PageSize = _pageSize;
        }

        /// <summary>
        /// 更新状态信息
        /// </summary>
        private void UpdateStatusInfo()
        {
            int selectedCount = NationalDataGrid.SelectedItems.Count;
            
            // 尝试更新主窗口的状态栏
            var mainWindow = Application.Current.MainWindow as MainWindow;
            mainWindow?.UpdateStatusBar(_totalRecords, selectedCount, PaginationControl.TotalPages);
        }

        /// <summary>
        /// 分页控件页码变化事件
        /// </summary>
        private void PaginationControl_PageChanged(object sender, PageChangedEventArgs e)
        {
            _currentPage = e.NewPage;
            ApplyPagination();
        }

        /// <summary>
        /// 分页控件页面大小变化事件
        /// </summary>
        private void PaginationControl_PageSizeChanged(object sender, PageSizeChangedEventArgs e)
        {
            _pageSize = e.NewPageSize;
            _currentPage = 1; // 重置到第一页
            ApplyPagination();
        }

        /// <summary>
        /// 新增按钮点击事件
        /// </summary>
        private void AddButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var editWindow = new EditWindow("新增国保联数据", _columnNames);
                if (editWindow.ShowDialog() == true && editWindow.IsSaved)
                {
                    var newData = new NationalData();
                    newData.SetAllData(editWindow.ResultData);
                    
                    _allData.Add(newData);
                    
                    // 保存到Excel
                    if (_excelService != null)
                    {
                        _excelService.AddRow(SheetName, editWindow.ResultData);
                        _excelService.Save();
                    }
                    
                    ApplyPagination();
                    MessageBox.Show("数据添加成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加数据失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 编辑按钮点击事件
        /// </summary>
        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedItem = NationalDataGrid.SelectedItem as NationalData;
            if (selectedItem == null)
            {
                MessageBox.Show("请选择要编辑的记录！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var editWindow = new EditWindow("编辑国保联数据", _columnNames, selectedItem.GetAllData());
                if (editWindow.ShowDialog() == true && editWindow.IsSaved)
                {
                    selectedItem.SetAllData(editWindow.ResultData);
                    
                    // 保存到Excel
                    if (_excelService != null)
                    {
                        var rowIndex = _allData.IndexOf(selectedItem) + 1; // +1因为第0行是表头
                        _excelService.UpdateRow(SheetName, rowIndex, editWindow.ResultData);
                        _excelService.Save();
                    }
                    
                    ApplyPagination();
                    MessageBox.Show("数据更新成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"编辑数据失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 删除按钮点击事件
        /// </summary>
        private void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedItem = NationalDataGrid.SelectedItem as NationalData;
            if (selectedItem == null)
            {
                MessageBox.Show("请选择要删除的记录！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show("确定要删除这条记录吗？", "确认删除", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    var rowIndex = _allData.IndexOf(selectedItem) + 1; // +1因为第0行是表头
                    _allData.Remove(selectedItem);
                    
                    // 从Excel删除
                    if (_excelService != null)
                    {
                        _excelService.DeleteRow(SheetName, rowIndex);
                        _excelService.Save();
                    }
                    
                    ApplyPagination();
                    MessageBox.Show("数据删除成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"删除数据失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 搜索按钮点击事件
        /// </summary>
        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            _currentPage = 1;
            ApplyPagination();
        }

        /// <summary>
        /// 导出按钮点击事件
        /// </summary>
        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("导出功能开发中...", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 打印按钮点击事件
        /// </summary>
        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("打印功能开发中...", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                InitializeData();
                MessageBox.Show("数据刷新成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"刷新数据失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
