# NAVI - 社会保障工具系统

## 功能概述

这是一个基于WPF的社会保障工具系统，主要功能包括：

1. **Excel数据管理** - 读取和操作Excel文件中的数据
2. **分页显示** - 支持大量数据的分页浏览
3. **数据编辑** - 支持新增、编辑、删除数据记录
4. **多数据源支持** - 支持事业者数据、国保联数据、服务代码数据

## 主要特性

### 1. Excel数据源
- 使用NPOI库读取和写入Excel文件
- 支持动态列生成
- 自动保存修改到Excel文件

### 2. 分页控件
- 自定义分页控件，支持页面大小调整
- 支持页码跳转
- 显示总记录数、当前页信息

### 3. 编辑功能
- 通用编辑窗口，支持动态字段
- 根据字段类型自动选择合适的输入控件
- 数据验证和错误处理

### 4. 用户界面
- 使用Material Design主题
- 现代化的UI设计
- 响应式布局

## 数据结构

### 事业者数据
- 事业者编号、邮政编码、事业者名称
- 代表者信息、联系方式
- 服务类别、附加服务对象等

### 国保联数据
- 保险者番号、被保险者番号
- 个人信息（姓名、生年月日、性别等）
- 住所、联系方式、认定信息

### 服务代码数据
- 服务代码、服务名称、服务分类
- 基本报酬、地域加算
- 有效期间等

## 使用说明

### 1. 登录
- 启动应用程序后，首先显示登录界面
- 输入用户ID和密码进行登录

### 2. 数据浏览
- 登录后，左侧导航菜单显示各种数据类型
- 点击相应菜单项可切换到对应的数据管理页面

### 3. 数据操作
- **新增记录**: 点击"新增记录"按钮，在弹出的编辑窗口中输入数据
- **编辑记录**: 选择要编辑的行，点击"编辑"按钮
- **删除记录**: 选择要删除的行，点击"删除"按钮，确认后删除

### 4. 搜索功能
- 在搜索框中输入关键字进行数据过滤
- 支持多字段模糊搜索

### 5. 分页操作
- 使用底部分页控件进行页面导航
- 可以调整每页显示的记录数
- 支持直接跳转到指定页面

## 技术架构

### 前端技术
- **WPF** - Windows Presentation Foundation
- **Material Design** - UI主题库
- **XAML** - 界面标记语言

### 数据处理
- **NPOI** - Excel文件操作库
- **动态数据绑定** - 支持灵活的数据结构

### 设计模式
- **MVVM模式** - 数据绑定和界面分离
- **控件复用** - 通用分页控件、编辑窗口
- **服务层** - Excel数据服务封装

## 文件结构

```
NAVI/
├── Controls/           # 自定义控件
│   └── PaginationControl.xaml(.cs)
├── Models/            # 数据模型
│   ├── BusinessData.cs
│   ├── NationalData.cs
│   └── ServiceCodeData.cs
├── Services/          # 服务层
│   └── ExcelDataService.cs
├── Windows/           # 窗口
│   └── EditWindow.xaml(.cs)
├── database/          # 数据文件
│   └── shortstay_app_ver0.9.xlsx
├── BusinessDataControl.xaml(.cs)    # 事业者数据控件
├── NationalDataControl.xaml(.cs)    # 国保联数据控件
├── ServiceCodeDataControl.xaml(.cs) # 服务代码数据控件
├── MainControl.xaml(.cs)            # 主控制界面
├── MainWindow.xaml(.cs)             # 主窗口
└── LoginControl.xaml(.cs)           # 登录控件
```

## 开发环境

- **.NET Framework 4.7.2**
- **Visual Studio 2019/2022**
- **Windows 10/11**

## 依赖包

- MaterialDesignThemes (4.9.0)
- MaterialDesignColors (2.1.4)
- NPOI (2.6.2)

## 注意事项

1. 首次运行时会自动创建测试Excel文件
2. 所有数据修改都会实时保存到Excel文件
3. 建议定期备份Excel数据文件
4. 确保有足够的磁盘空间存储数据文件

## 扩展功能

系统设计为可扩展架构，可以轻松添加：
- 新的数据类型和工作表
- 自定义字段验证规则
- 数据导入/导出功能
- 报表生成功能
- 用户权限管理

## 故障排除

### 常见问题
1. **Excel文件无法打开**: 检查文件路径和权限
2. **数据保存失败**: 确保Excel文件未被其他程序占用
3. **界面显示异常**: 检查Material Design主题是否正确加载

### 日志记录
系统会在出现错误时显示详细的错误信息，便于问题诊断。
