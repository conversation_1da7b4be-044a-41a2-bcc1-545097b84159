﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using NAVI.Models;

namespace NAVI
{
    /// <summary>
    /// BusinessDataControl.xaml 的交互逻辑
    /// </summary>
    public partial class BusinessDataControl : UserControl
    {
        private ObservableCollection<BusinessData> _businessDataList;
        private List<BusinessData> _allData;

        public BusinessDataControl()
        {
            InitializeComponent();
            InitializeData();
            SetupEventHandlers();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            // 获取模拟数据
            _allData = BusinessDataService.GetSampleData();
            _businessDataList = new ObservableCollection<BusinessData>(_allData);

            // 绑定数据到DataGrid
            BusinessDataGrid.ItemsSource = _businessDataList;

            // 更新状态栏信息
            UpdateStatusInfo();

            // 设置搜索框的占位符效果
            SetupSearchBoxPlaceholder();
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // DataGrid选择变化事件
            BusinessDataGrid.SelectionChanged += BusinessDataGrid_SelectionChanged;

            // 搜索框事件
            SearchTextBox.GotFocus += SearchTextBox_GotFocus;
            SearchTextBox.LostFocus += SearchTextBox_LostFocus;
            SearchTextBox.TextChanged += SearchTextBox_TextChanged;
        }

        /// <summary>
        /// 设置搜索框占位符效果
        /// </summary>
        private void SetupSearchBoxPlaceholder()
        {
            SearchTextBox.Foreground = new SolidColorBrush(System.Windows.Media.Colors.Gray);
        }

        /// <summary>
        /// 搜索框获得焦点事件
        /// </summary>
        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (SearchTextBox.Text == "输入事业者编号・事业者名称・其他关键字")
            {
                SearchTextBox.Text = "";
                SearchTextBox.Foreground = new SolidColorBrush(System.Windows.Media.Colors.Black);
            }
        }

        /// <summary>
        /// 搜索框失去焦点事件
        /// </summary>
        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                SearchTextBox.Text = "输入事业者编号・事业者名称・其他关键字";
                SearchTextBox.Foreground = new SolidColorBrush(System.Windows.Media.Colors.Gray);
            }
        }

        /// <summary>
        /// 搜索框文本变化事件
        /// </summary>
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            PerformSearch();
        }

        /// <summary>
        /// DataGrid选择变化事件
        /// </summary>
        private void BusinessDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateStatusInfo();
        }

        /// <summary>
        /// 执行搜索
        /// </summary>
        private void PerformSearch()
        {
            string searchText = SearchTextBox.Text;

            // 如果是占位符文本或空白，显示所有数据
            if (string.IsNullOrWhiteSpace(searchText) || searchText == "输入事业者编号・事业者名称・其他关键字")
            {
                _businessDataList.Clear();
                foreach (var item in _allData)
                {
                    _businessDataList.Add(item);
                }
            }
            else
            {
                // 执行搜索过滤
                var filteredData = _allData.Where(x =>
                    x.Number.Contains(searchText) ||
                    x.Name.Contains(searchText) ||
                    x.Address.Contains(searchText) ||
                    x.Leader.Contains(searchText) ||
                    x.Manager.Contains(searchText) ||
                    x.Phone.Contains(searchText)
                ).ToList();

                _businessDataList.Clear();
                foreach (var item in filteredData)
                {
                    _businessDataList.Add(item);
                }
            }

            UpdateStatusInfo();
        }

        /// <summary>
        /// 更新状态信息
        /// </summary>
        private void UpdateStatusInfo()
        {
            int totalCount = _businessDataList.Count;
            int selectedCount = BusinessDataGrid.SelectedItems.Count;

            // 尝试更新主窗口的状态栏
            var mainWindow = Application.Current.MainWindow as MainWindow;
            mainWindow?.UpdateStatusBar(totalCount, selectedCount, 1);
        }

        /// <summary>
        /// 按钮点击事件处理
        /// </summary>
        private void Button_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            if (button != null)
            {
                string action = button.Content.ToString();
                MessageBox.Show($"执行操作: {action}", "操作提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }
}
