﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace NAVI
{
    /// <summary>
    /// LoginControl.xaml 的交互逻辑
    /// </summary>
    public partial class BusinessDataControl : UserControl
    {
        //private MainWindow _main;
        public BusinessDataControl()
        {
            InitializeComponent();
            //_main = main;
        }
        /// <summary>
        /// 登录按钮点击事件
        /// </summary>
        private void Button_Click(object sender, RoutedEventArgs e)
        {
        }
    }
}
