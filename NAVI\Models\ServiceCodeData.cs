using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;

namespace NAVI.Models
{
    /// <summary>
    /// 服务代码数据模型
    /// </summary>
    public class ServiceCodeData : INotifyPropertyChanged
    {
        private Dictionary<string, object> _data = new Dictionary<string, object>();

        /// <summary>
        /// 动态属性访问器
        /// </summary>
        public object this[string propertyName]
        {
            get
            {
                return _data.ContainsKey(propertyName) ? _data[propertyName] : null;
            }
            set
            {
                _data[propertyName] = value;
                OnPropertyChanged(propertyName);
            }
        }

        /// <summary>
        /// 获取所有属性名
        /// </summary>
        public IEnumerable<string> PropertyNames => _data.Keys;

        /// <summary>
        /// 获取所有数据
        /// </summary>
        public Dictionary<string, object> GetAllData()
        {
            return new Dictionary<string, object>(_data);
        }

        /// <summary>
        /// 设置所有数据
        /// </summary>
        public void SetAllData(Dictionary<string, object> data)
        {
            _data = new Dictionary<string, object>(data);
            OnPropertyChanged(string.Empty); // 通知所有属性变化
        }

        /// <summary>
        /// 检查是否包含属性
        /// </summary>
        public bool HasProperty(string propertyName)
        {
            return _data.ContainsKey(propertyName);
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 服务代码数据服务类
    /// </summary>
    public static class ServiceCodeDataService
    {
        /// <summary>
        /// 从字典数据创建服务代码数据列表
        /// </summary>
        public static List<ServiceCodeData> CreateFromDictionaries(List<Dictionary<string, object>> dictionaries)
        {
            var result = new List<ServiceCodeData>();
            
            foreach (var dict in dictionaries)
            {
                var serviceCodeData = new ServiceCodeData();
                serviceCodeData.SetAllData(dict);
                result.Add(serviceCodeData);
            }
            
            return result;
        }

        /// <summary>
        /// 将服务代码数据列表转换为字典列表
        /// </summary>
        public static List<Dictionary<string, object>> ConvertToDictionaries(List<ServiceCodeData> serviceCodeDataList)
        {
            return serviceCodeDataList.Select(scd => scd.GetAllData()).ToList();
        }

        /// <summary>
        /// 获取模拟数据（如果Excel文件不存在时使用）
        /// </summary>
        public static List<ServiceCodeData> GetSampleData()
        {
            var sampleDictionaries = new List<Dictionary<string, object>>
            {
                new Dictionary<string, object>
                {
                    ["服务代码"] = "110101",
                    ["服务名称"] = "居宅介护",
                    ["服务分类"] = "居宅服务",
                    ["单位"] = "回",
                    ["基本报酬"] = 2500,
                    ["地域加算"] = "有",
                    ["备注"] = "基本的な身体介護サービス",
                    ["有效期间开始"] = "2023/04/01",
                    ["有效期间结束"] = "2024/03/31"
                },
                new Dictionary<string, object>
                {
                    ["服务代码"] = "110201",
                    ["服务名称"] = "居宅介护（生活援助）",
                    ["服务分类"] = "居宅服务",
                    ["单位"] = "回",
                    ["基本报酬"] = 1800,
                    ["地域加算"] = "有",
                    ["备注"] = "生活援助中心のサービス",
                    ["有效期间开始"] = "2023/04/01",
                    ["有效期间结束"] = "2024/03/31"
                },
                new Dictionary<string, object>
                {
                    ["服务代码"] = "120101",
                    ["服务名称"] = "重度访问介护",
                    ["服务分类"] = "居宅服务",
                    ["单位"] = "时间",
                    ["基本报酬"] = 4200,
                    ["地域加算"] = "有",
                    ["备注"] = "重度の障害者向けサービス",
                    ["有效期间开始"] = "2023/04/01",
                    ["有效期间结束"] = "2024/03/31"
                },
                new Dictionary<string, object>
                {
                    ["服务代码"] = "130101",
                    ["服务名称"] = "同行援护",
                    ["服务分类"] = "居宅服务",
                    ["单位"] = "回",
                    ["基本报酬"] = 3200,
                    ["地域加算"] = "无",
                    ["备注"] = "外出時の同行支援サービス",
                    ["有效期间开始"] = "2023/04/01",
                    ["有效期间结束"] = "2024/03/31"
                },
                new Dictionary<string, object>
                {
                    ["服务代码"] = "210101",
                    ["服务名称"] = "生活介护",
                    ["服务分类"] = "日间活动",
                    ["单位"] = "日",
                    ["基本报酬"] = 6800,
                    ["地域加算"] = "有",
                    ["备注"] = "日中活動支援サービス",
                    ["有效期间开始"] = "2023/04/01",
                    ["有效期间结束"] = "2024/03/31"
                }
            };

            return CreateFromDictionaries(sampleDictionaries);
        }
    }
}
