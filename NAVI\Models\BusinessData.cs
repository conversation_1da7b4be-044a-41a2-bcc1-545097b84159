using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;

namespace NAVI.Models
{
    /// <summary>
    /// 事业者管理数据模型
    /// </summary>
    public class BusinessData : INotifyPropertyChanged
    {
        private Dictionary<string, object> _data = new Dictionary<string, object>();

        /// <summary>
        /// 动态属性访问器
        /// </summary>
        public object this[string propertyName]
        {
            get
            {
                return _data.ContainsKey(propertyName) ? _data[propertyName] : null;
            }
            set
            {
                _data[propertyName] = value;
                OnPropertyChanged(propertyName);
            }
        }

        /// <summary>
        /// 获取所有属性名
        /// </summary>
        public IEnumerable<string> PropertyNames => _data.Keys;

        /// <summary>
        /// 获取所有数据
        /// </summary>
        public Dictionary<string, object> GetAllData()
        {
            return new Dictionary<string, object>(_data);
        }

        /// <summary>
        /// 设置所有数据
        /// </summary>
        public void SetAllData(Dictionary<string, object> data)
        {
            _data = new Dictionary<string, object>(data);
            OnPropertyChanged(string.Empty); // 通知所有属性变化
        }

        /// <summary>
        /// 检查是否包含属性
        /// </summary>
        public bool HasProperty(string propertyName)
        {
            return _data.ContainsKey(propertyName);
        }

        // 保留原有属性以兼容现有绑定
        private int _no;
        private string _number;
        private string _zip;
        private string _address;
        private string _name;
        private string _position;
        private string _leader;
        private string _manager;
        private string _phone;

        /// <summary>
        /// 序号
        /// </summary>
        public int No
        {
            get { return _no; }
            set
            {
                _no = value;
                OnPropertyChanged(nameof(No));
            }
        }

        /// <summary>
        /// 事业者编号
        /// </summary>
        public string Number
        {
            get { return _number; }
            set
            {
                _number = value;
                OnPropertyChanged(nameof(Number));
            }
        }

        /// <summary>
        /// 邮政编码
        /// </summary>
        public string Zip
        {
            get { return _zip; }
            set
            {
                _zip = value;
                OnPropertyChanged(nameof(Zip));
            }
        }

        /// <summary>
        /// 所在地地址
        /// </summary>
        public string Address
        {
            get { return _address; }
            set
            {
                _address = value;
                OnPropertyChanged(nameof(Address));
            }
        }

        /// <summary>
        /// 事业者名称
        /// </summary>
        public string Name
        {
            get { return _name; }
            set
            {
                _name = value;
                OnPropertyChanged(nameof(Name));
            }
        }

        /// <summary>
        /// 代表者职务
        /// </summary>
        public string Position
        {
            get { return _position; }
            set
            {
                _position = value;
                OnPropertyChanged(nameof(Position));
            }
        }

        /// <summary>
        /// 代表者姓名
        /// </summary>
        public string Leader
        {
            get { return _leader; }
            set
            {
                _leader = value;
                OnPropertyChanged(nameof(Leader));
            }
        }

        /// <summary>
        /// 负责人姓名
        /// </summary>
        public string Manager
        {
            get { return _manager; }
            set
            {
                _manager = value;
                OnPropertyChanged(nameof(Manager));
            }
        }

        /// <summary>
        /// 联系方式
        /// </summary>
        public string Phone
        {
            get { return _phone; }
            set
            {
                _phone = value;
                OnPropertyChanged(nameof(Phone));
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 事业者数据服务类
    /// </summary>
    public static class BusinessDataService
    {
        /// <summary>
        /// 从字典数据创建事业者数据列表
        /// </summary>
        public static List<BusinessData> CreateFromDictionaries(List<Dictionary<string, object>> dictionaries)
        {
            var result = new List<BusinessData>();

            foreach (var dict in dictionaries)
            {
                var businessData = new BusinessData();
                businessData.SetAllData(dict);
                result.Add(businessData);
            }

            return result;
        }

        /// <summary>
        /// 将事业者数据列表转换为字典列表
        /// </summary>
        public static List<Dictionary<string, object>> ConvertToDictionaries(List<BusinessData> businessDataList)
        {
            return businessDataList.Select(bd => bd.GetAllData()).ToList();
        }

        /// <summary>
        /// 获取模拟数据
        /// </summary>
        /// <returns></returns>
        public static List<BusinessData> GetSampleData()
        {
            return new List<BusinessData>
            {
                new BusinessData
                {
                    No = 1,
                    Number = "wqr453cff",
                    Zip = "〒168-8577",
                    Address = "东京都杉并区高井戸东3丁目...",
                    Name = "サポートハウス东京",
                    Position = "代表取缔役社长",
                    Leader = "中村美香",
                    Manager = "铃木太郎",
                    Phone = "03-1234-5676"
                },
                new BusinessData
                {
                    No = 2,
                    Number = "fcwcw34",
                    Zip = "〒190-8578",
                    Address = "东京都立川市荣町4丁目...",
                    Name = "グリーンケア荣町",
                    Position = "代表取缔役社长",
                    Leader = "田中正人",
                    Manager = "山田花子",
                    Phone = "************"
                },
                new BusinessData
                {
                    No = 3,
                    Number = "1122334455",
                    Zip = "〒192-8579",
                    Address = "东京都八王子市中野町7丁目...",
                    Name = "光明福祉センター",
                    Position = "代表取缔役社长",
                    Leader = "佐藤茂夫",
                    Manager = "伊藤浩介",
                    Phone = "************"
                },
                new BusinessData
                {
                    No = 4,
                    Number = "5566778899",
                    Zip = "〒153-8580",
                    Address = "东京都目黑区中目黑1-10-1...",
                    Name = "和みグループホーム",
                    Position = "代表取缔役社长",
                    Leader = "小川雄一郎",
                    Manager = "川上美纪",
                    Phone = "03-5678-9012"
                },
                new BusinessData
                {
                    No = 5,
                    Number = "9988776655",
                    Zip = "〒154-8581",
                    Address = "东京都世田谷区三軒茶屋2-15-8...",
                    Name = "みらい福祉作业所",
                    Position = "代表取缔役社长",
                    Leader = "高橋健太",
                    Manager = "松本由美",
                    Phone = "03-9876-5432"
                },
                new BusinessData
                {
                    No = 6,
                    Number = "1357924680",
                    Zip = "〒155-8582",
                    Address = "东京都世田谷区下北沢3-20-5...",
                    Name = "希望の里デイサービス",
                    Position = "代表取缔役社长",
                    Leader = "渡辺直子",
                    Manager = "森田智也",
                    Phone = "03-2468-1357"
                }
            };
        }
    }
}
