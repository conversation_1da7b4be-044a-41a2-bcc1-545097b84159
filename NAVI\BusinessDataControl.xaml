<UserControl x:Class="NAVI.BusinessDataControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="500" d:DesignWidth="900">
    <Grid Background="White">
        <StackPanel Margin="16">
            <!-- 标题 -->
            <TextBlock Text="事业者管理数据" FontSize="22" FontWeight="Bold" Margin="0,0,0,12"/>
            <!-- 搜索栏和按钮区 -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                <TextBox Width="260" Height="28" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <Button Content="搜索" Width="60" Height="28" Margin="0,0,8,0"/>
                <Button Content="导出" Width="60" Height="28" Margin="0,0,8,0"/>
                <Button Content="打印" Width="60" Height="28" Margin="0,0,8,0"/>
                <Button Content="刷新" Width="60" Height="28"/>
            </StackPanel>
            <!-- 表格 -->
            <DataGrid AutoGenerateColumns="False" Height="340" Margin="0,0,0,0">
                <DataGrid.Columns>
                    <DataGridTemplateColumn Header="操作" Width="90">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Content="编辑" Width="36" Height="22" Margin="0,0,4,0"/>
                                    <Button Content="删除" Width="36" Height="22" Background="#F8B6B6"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTextColumn Header="No" Width="40" Binding="{Binding No}"/>
                    <DataGridTextColumn Header="事业者编号" Width="100" Binding="{Binding Number}"/>
                    <DataGridTextColumn Header="邮政编码" Width="90" Binding="{Binding Zip}"/>
                    <DataGridTextColumn Header="所在地地址" Width="200" Binding="{Binding Address}"/>
                    <DataGridTextColumn Header="事业者名称" Width="160" Binding="{Binding Name}"/>
                    <DataGridTextColumn Header="代表者职务" Width="120" Binding="{Binding Position}"/>
                    <DataGridTextColumn Header="代表者姓名" Width="100" Binding="{Binding Leader}"/>
                    <DataGridTextColumn Header="负责人姓名" Width="100" Binding="{Binding Manager}"/>
                    <DataGridTextColumn Header="联系方式" Width="120" Binding="{Binding Phone}"/>
                </DataGrid.Columns>
            </DataGrid>
        </StackPanel>
    </Grid>
</UserControl> 