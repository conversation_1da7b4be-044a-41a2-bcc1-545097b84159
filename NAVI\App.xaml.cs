﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace NAVI
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // 创建测试Excel文件（如果不存在）
            try
            {
                var excelPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "database", "shortstay_app_ver0.9.xlsx");
                if (!File.Exists(excelPath))
                {
                    //TestExcelCreator.CreateTestExcelFile();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建测试Excel文件失败：{ex.Message}", "警告",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
    }
}
