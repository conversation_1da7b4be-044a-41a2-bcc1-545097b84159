using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace NAVI.Windows
{
    /// <summary>
    /// 通用编辑窗口
    /// </summary>
    public partial class EditWindow : Window
    {
        private Dictionary<string, object> _originalData;
        private Dictionary<string, Control> _fieldControls;
        private List<string> _columnNames;

        /// <summary>
        /// 编辑结果数据
        /// </summary>
        public Dictionary<string, object> ResultData { get; private set; }

        /// <summary>
        /// 是否保存
        /// </summary>
        public bool IsSaved { get; private set; }

        public EditWindow(string title, List<string> columnNames, Dictionary<string, object> data = null)
        {
            InitializeComponent();
            
            TitleTextBlock.Text = title;
            _columnNames = columnNames;
            _originalData = data ?? new Dictionary<string, object>();
            _fieldControls = new Dictionary<string, Control>();
            
            CreateFields();
            LoadData();
        }

        /// <summary>
        /// 创建字段
        /// </summary>
        private void CreateFields()
        {
            FieldsGrid.RowDefinitions.Clear();
            FieldsGrid.Children.Clear();
            
            int rowCount = (int)Math.Ceiling(_columnNames.Count / 2.0);
            
            // 创建行定义
            for (int i = 0; i < rowCount; i++)
            {
                FieldsGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            }

            // 创建字段控件
            for (int i = 0; i < _columnNames.Count; i++)
            {
                var columnName = _columnNames[i];
                var row = i / 2;
                var col = i % 2;

                // 创建字段容器
                var fieldContainer = new StackPanel
                {
                    Orientation = Orientation.Vertical,
                    Margin = new Thickness(0, 0, col == 0 ? 20 : 0, 16)
                };

                // 创建标签
                var label = new TextBlock
                {
                    Text = columnName,
                    Style = (Style)FindResource("FieldLabelStyle")
                };

                // 创建输入控件
                Control inputControl = CreateInputControl(columnName);
                _fieldControls[columnName] = inputControl;

                fieldContainer.Children.Add(label);
                fieldContainer.Children.Add(inputControl);

                Grid.SetRow(fieldContainer, row);
                Grid.SetColumn(fieldContainer, col);
                FieldsGrid.Children.Add(fieldContainer);
            }
        }

        /// <summary>
        /// 创建输入控件
        /// </summary>
        private Control CreateInputControl(string columnName)
        {
            // 根据字段名称决定控件类型
            if (IsMultiLineField(columnName))
            {
                return new TextBox
                {
                    Style = (Style)FindResource("MultiLineTextBoxStyle")
                };
            }
            else if (IsComboBoxField(columnName))
            {
                var comboBox = new ComboBox
                {
                    Style = (Style)FindResource("FieldComboBoxStyle")
                };
                
                // 根据字段添加选项
                PopulateComboBoxItems(comboBox, columnName);
                return comboBox;
            }
            else
            {
                return new TextBox
                {
                    Style = (Style)FindResource("FieldTextBoxStyle")
                };
            }
        }

        /// <summary>
        /// 判断是否为多行字段
        /// </summary>
        private bool IsMultiLineField(string columnName)
        {
            var multiLineFields = new[] { "备注", "说明", "用户信息", "培训受讲说明", "所在地地址", "住所" };
            return multiLineFields.Any(field => columnName.Contains(field));
        }

        /// <summary>
        /// 判断是否为下拉框字段
        /// </summary>
        private bool IsComboBoxField(string columnName)
        {
            var comboBoxFields = new[] { "性别", "服务分类", "地域加算", "附加服务对象", "服务类别" };
            return comboBoxFields.Any(field => columnName.Contains(field));
        }

        /// <summary>
        /// 填充下拉框选项
        /// </summary>
        private void PopulateComboBoxItems(ComboBox comboBox, string columnName)
        {
            if (columnName.Contains("性别"))
            {
                comboBox.Items.Add("男");
                comboBox.Items.Add("女");
            }
            else if (columnName.Contains("服务分类") || columnName.Contains("服务类别"))
            {
                comboBox.Items.Add("居宅服务");
                comboBox.Items.Add("日间活动");
                comboBox.Items.Add("居住支援");
                comboBox.Items.Add("其他");
            }
            else if (columnName.Contains("地域加算"))
            {
                comboBox.Items.Add("有");
                comboBox.Items.Add("无");
            }
            else if (columnName.Contains("附加服务对象"))
            {
                comboBox.Items.Add("医疗连携体制补贴");
                comboBox.Items.Add("共同生活援助");
                comboBox.Items.Add("其他");
            }
        }

        /// <summary>
        /// 加载数据
        /// </summary>
        private void LoadData()
        {
            foreach (var kvp in _originalData)
            {
                if (_fieldControls.ContainsKey(kvp.Key))
                {
                    var control = _fieldControls[kvp.Key];
                    var value = kvp.Value?.ToString() ?? string.Empty;

                    if (control is TextBox textBox)
                    {
                        textBox.Text = value;
                    }
                    else if (control is ComboBox comboBox)
                    {
                        comboBox.Text = value;
                    }
                }
            }
        }

        /// <summary>
        /// 收集数据
        /// </summary>
        private Dictionary<string, object> CollectData()
        {
            var result = new Dictionary<string, object>();

            foreach (var kvp in _fieldControls)
            {
                var columnName = kvp.Key;
                var control = kvp.Value;
                object value = null;

                if (control is TextBox textBox)
                {
                    value = textBox.Text;
                }
                else if (control is ComboBox comboBox)
                {
                    value = comboBox.Text;
                }

                result[columnName] = value ?? string.Empty;
            }

            return result;
        }

        /// <summary>
        /// 验证数据
        /// </summary>
        private bool ValidateData()
        {
            // 这里可以添加具体的验证逻辑
            // 例如检查必填字段、格式验证等
            return true;
        }

        /// <summary>
        /// 保存按钮点击事件
        /// </summary>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var data = CollectData();
                
                if (!ValidateData())
                {
                    MessageBox.Show("请检查输入的数据格式！", "验证失败", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                ResultData = data;
                IsSaved = true;
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存数据时发生错误：{ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            IsSaved = false;
            DialogResult = false;
            Close();
        }
    }
}
