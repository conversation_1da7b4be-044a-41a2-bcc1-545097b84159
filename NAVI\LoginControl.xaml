﻿<UserControl x:Class="NAVI.LoginControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:NAVI"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">
    <Grid Background="#F5F5F5">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="4*"/>
        </Grid.ColumnDefinitions>
        <!-- 左侧蓝色介绍区 -->
        <Border Grid.Column="0" Background="#2986A8">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center" Width="320">
                <TextBlock Text="都加算NAVI" FontSize="32" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="【仮称】" FontSize="18" Foreground="White" HorizontalAlignment="Center" Margin="0,8,0,24"/>
                <TextBlock Foreground="White" FontSize="15" TextWrapping="Wrap">
                    地方自治体障害福祉課職員専用业务管理系统
                </TextBlock>
                <StackPanel Margin="0,16,0,0">
                    <TextBlock Foreground="White" FontSize="14" Text="・青业务者申请数据自动导入处理"/>
                    <TextBlock Foreground="White" FontSize="14" Text="・国保性数据智能匹配验证"/>
                    <TextBlock Foreground="White" FontSize="14" Text="・财务报告和补助金申报自动生成"/>
                    <TextBlock Foreground="White" FontSize="14" Text="・支持离线环境安全运行"/>
                </StackPanel>
            </StackPanel>
        </Border>
        <!-- 右侧登录表单区 -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center" Width="350" >
                <StackPanel Margin="0,0,0,24">
                    <TextBlock Text="登录用户名" FontWeight="Bold" FontSize="15" Margin="0,0,0,4"/>
                    <TextBox Height="32" FontSize="16" Name="userName"  TextAlignment="Left" HorizontalContentAlignment="Left"   VerticalContentAlignment="Center" />
                    <TextBlock Text="登录密码" FontWeight="Bold"  FontSize="15" Margin="0,16,0,4"/>
                    <PasswordBox Height="32" FontSize="16" Name="passWord" HorizontalContentAlignment="Left"   VerticalContentAlignment="Center"/>
                    <Button Content="登录" Height="38" Margin="0,24,0,0" Background="#2986A8" Foreground="White" FontWeight="Bold" FontSize="16" Click="Button_Click"/>
                </StackPanel>
                <!-- 帮助链接 -->
                <Border Background="#F8FBFD" CornerRadius="8" Padding="16" Margin="0,0,0,24">
                    <StackPanel>
                        <TextBlock Text="如需帮助请点击以下链接..." FontWeight="Bold" FontSize="15" Margin="0,0,0,8"/>
                        <StackPanel>
                            <TextBlock Text="📚 查看操作手册" Foreground="#2986A8" Cursor="Hand" Margin="0,2"/>
                            <TextBlock Text="❓ 常见问题模板" Foreground="#2986A8" Cursor="Hand" Margin="0,2"/>
                            <TextBlock Text="🛠 联系技术支持" Foreground="#2986A8" Cursor="Hand" Margin="0,2"/>
                            <TextBlock Text="🔗 实用工具资源" Foreground="#2986A8" Cursor="Hand" Margin="0,2"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
            <!-- 系统入口按钮 -->
            <StackPanel Grid.Row="1" Orientation="Vertical" HorizontalAlignment="Center" Width="700" Margin="0,0,0,8">
                <TextBlock Text="自治体障害福祉課職員様へのご案内" FontWeight="Bold" FontSize="16" HorizontalAlignment="Left" Margin="0,0,0,8"/>
                <WrapPanel HorizontalAlignment="Center" ItemWidth="160" ItemHeight="40" Margin="0,0,0,0">
                    <Button Content="障害者认定管理システム" Width="160" Height="40" Margin="8,4" Background="#2986A8" Foreground="White"/>
                    <Button Content="移动支援管理システム" Width="160" Height="40" Margin="8,4" Background="#2986A8" Foreground="White"/>
                    <Button Content="认定审査会管理システム" Width="160" Height="40" Margin="8,4" Background="#2986A8" Foreground="White"/>
                    <Button Content="その他サービス" Width="160" Height="40" Margin="8,4" Background="#2986A8" Foreground="White"/>
                </WrapPanel>
            </StackPanel>
            <!-- 版权信息 -->
            <StackPanel Grid.Row="2" VerticalAlignment="Bottom" HorizontalAlignment="Center" Margin="0,0,0,8">
                <TextBlock Text="系统版本 ver1.105" FontSize="12" Foreground="#888" HorizontalAlignment="Center"/>
                <TextBlock Text="Copyright © 2025 terabox.co.ltd All Rights Reserved" FontSize="12" Foreground="#888" HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
