<UserControl x:Class="NAVI.Controls.PaginationControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="50" d:DesignWidth="800">
    
    <UserControl.Resources>
        <!-- 分页按钮样式 -->
        <Style x:Key="PaginationButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#FF2986A8"/>
            <Setter Property="BorderBrush" Value="#FFE0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Width" Value="36"/>
            <Setter Property="Height" Value="36"/>
            <Setter Property="Margin" Value="2,0"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                               BorderBrush="{TemplateBinding BorderBrush}" 
                               BorderThickness="{TemplateBinding BorderThickness}" 
                               CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FFF0F8FF"/>
                                <Setter Property="BorderBrush" Value="#FF2986A8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FFE3F2FD"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 当前页按钮样式 -->
        <Style x:Key="CurrentPageButtonStyle" TargetType="Button" BasedOn="{StaticResource PaginationButtonStyle}">
            <Setter Property="Background" Value="#FF2986A8"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#FF2986A8"/>
        </Style>
        
        <!-- 页面大小选择样式 -->
        <Style x:Key="PageSizeComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#FFE0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="Width" Value="80"/>
            <Setter Property="FontSize" Value="13"/>
        </Style>
    </UserControl.Resources>
    
    <Border Background="White" 
           BorderBrush="#FFE0E0E0" 
           BorderThickness="1,0,1,1" 
           Padding="16,8">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- 左侧信息 -->
            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                <TextBlock Text="每页显示:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <ComboBox x:Name="PageSizeComboBox" 
                         Style="{StaticResource PageSizeComboBoxStyle}"
                         SelectionChanged="PageSizeComboBox_SelectionChanged">
                    <ComboBoxItem Content="10" IsSelected="True"/>
                    <ComboBoxItem Content="20"/>
                    <ComboBoxItem Content="50"/>
                    <ComboBoxItem Content="100"/>
                </ComboBox>
                <TextBlock Text="条" VerticalAlignment="Center" Margin="8,0,16,0"/>
                
                <TextBlock x:Name="InfoTextBlock" 
                          Text="共0条记录，第0页/共0页" 
                          VerticalAlignment="Center" 
                          Foreground="#FF666666"/>
            </StackPanel>
            
            <!-- 中间空白 -->
            <Grid Grid.Column="1"/>
            
            <!-- 页码跳转 -->
            <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,16,0">
                <TextBlock Text="跳转到:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <TextBox x:Name="JumpPageTextBox" 
                        Width="60" Height="32" 
                        VerticalContentAlignment="Center" 
                        HorizontalContentAlignment="Center"
                        BorderBrush="#FFE0E0E0"
                        KeyDown="JumpPageTextBox_KeyDown"/>
                <Button Content="跳转" 
                       Style="{StaticResource PaginationButtonStyle}" 
                       Width="50" 
                       Margin="8,0,0,0"
                       Click="JumpButton_Click"/>
            </StackPanel>
            
            <!-- 分页按钮 -->
            <StackPanel Grid.Column="3" Orientation="Horizontal" VerticalAlignment="Center">
                <!-- 首页 -->
                <Button x:Name="FirstPageButton" 
                       Content="首页" 
                       Style="{StaticResource PaginationButtonStyle}" 
                       Width="50"
                       Click="FirstPageButton_Click"/>
                
                <!-- 上一页 -->
                <Button x:Name="PrevPageButton" 
                       Style="{StaticResource PaginationButtonStyle}"
                       Click="PrevPageButton_Click">
                    <materialDesign:PackIcon Kind="ChevronLeft" Width="16" Height="16"/>
                </Button>
                
                <!-- 页码按钮容器 -->
                <StackPanel x:Name="PageNumbersPanel" Orientation="Horizontal"/>
                
                <!-- 下一页 -->
                <Button x:Name="NextPageButton" 
                       Style="{StaticResource PaginationButtonStyle}"
                       Click="NextPageButton_Click">
                    <materialDesign:PackIcon Kind="ChevronRight" Width="16" Height="16"/>
                </Button>
                
                <!-- 末页 -->
                <Button x:Name="LastPageButton" 
                       Content="末页" 
                       Style="{StaticResource PaginationButtonStyle}" 
                       Width="50"
                       Click="LastPageButton_Click"/>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
