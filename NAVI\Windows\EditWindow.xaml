<Window x:Class="NAVI.Windows.EditWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="NAVI信息编辑" 
        Height="600" Width="900"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        Background="#FFF8F9FA">
    
    <Window.Resources>
        <!-- 标签样式 -->
        <Style x:Key="FieldLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#FF333333"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,8,0"/>
        </Style>
        
        <!-- 输入框样式 -->
        <Style x:Key="FieldTextBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="36"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderBrush" Value="#FFCCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                               BorderBrush="{TemplateBinding BorderBrush}" 
                               BorderThickness="{TemplateBinding BorderThickness}" 
                               CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                         Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#FF2986A8"/>
                                <Setter Property="BorderThickness" Value="2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 下拉框样式 -->
        <Style x:Key="FieldComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Height" Value="36"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderBrush" Value="#FFCCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <!-- 日期选择器样式 -->
        <Style x:Key="FieldDatePickerStyle" TargetType="DatePicker">
            <Setter Property="Height" Value="36"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="BorderBrush" Value="#FFCCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Padding" Value="8,6"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#FF2986A8"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <!-- 多行文本框样式 -->
        <Style x:Key="MultiLineTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource FieldTextBoxStyle}">
            <Setter Property="Height" Value="80"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="AcceptsReturn" Value="True"/>
            <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
        </Style>
        
        <!-- 按钮样式 -->
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="36"/>
            <Setter Property="MinWidth" Value="80"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="8,0,0,0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                               CornerRadius="4" 
                               Padding="16,8">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 保存按钮样式 -->
        <Style x:Key="SaveButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Background" Value="#FF2986A8"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>
        
        <!-- 取消按钮样式 -->
        <Style x:Key="CancelButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Background" Value="#FF757575"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <Border Grid.Row="0" 
               Background="#FF2986A8" 
               Padding="20,16">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="Edit" 
                                       Width="24" Height="24" 
                                       Foreground="White" 
                                       VerticalAlignment="Center" 
                                       Margin="0,0,12,0"/>
                <TextBlock x:Name="TitleTextBlock" 
                          Text="事业者信息编辑" 
                          FontSize="18" 
                          FontWeight="Medium" 
                          Foreground="White" 
                          VerticalAlignment="Center"/>
            </StackPanel>
        </Border>
        
        <!-- 内容区域 -->
        <ScrollViewer Grid.Row="1"
                     VerticalScrollBarVisibility="Auto"
                     HorizontalScrollBarVisibility="Disabled"
                     Padding="40,30">
            <Border Background="White"
                   CornerRadius="8"
                   Padding="30,25"
                   BorderBrush="#FFE0E0E0"
                   BorderThickness="1"
                   Effect="{x:Null}">
                <!--<Border.Effect>
                    <DropShadowEffect Color="#FFE0E0E0"
                                     Direction="270"
                                     ShadowDepth="2"
                                     BlurRadius="8"
                                     Opacity="0.3"/>
                </Border.Effect>-->
                <Grid x:Name="FieldsGrid">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <!-- 动态生成的字段将在这里添加 -->
                </Grid>
            </Border>
        </ScrollViewer>
        
        <!-- 按钮区域 -->
        <Border Grid.Row="2" 
               Background="White" 
               BorderBrush="#FFE0E0E0" 
               BorderThickness="0,1,0,0" 
               Padding="20,16">
            <StackPanel Orientation="Horizontal" 
                       HorizontalAlignment="Right">
                <Button x:Name="CancelButton" 
                       Content="取消" 
                       Style="{StaticResource CancelButtonStyle}"
                       Click="CancelButton_Click"/>
                <Button x:Name="SaveButton" 
                       Content="保存" 
                       Style="{StaticResource SaveButtonStyle}"
                       Click="SaveButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
