﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace NAVI
{
    /// <summary>
    /// MainControl.xaml 的交互逻辑
    /// </summary>
    public partial class MainControl : UserControl
    {
        public MainControl()
        {
            InitializeComponent();
        }

        private void MenuTree_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            var item = (e.NewValue as TreeViewItem);
            if (item == null || item.Tag == null) return;
            switch (item.Tag.ToString())
            {
                case "BusinessData":
                    MainContent.Content = new BusinessDataControl();
                    break;
                case "NationalData":
                    //MainContent.Content = new NationalDataControl();
                    break;
                case "MatchData":
                    //MainContent.Content = new MatchDataControl();
                    break;
                // 其他case可按需添加
                default:
                    MainContent.Content = null;
                    break;
            }
        }
    }
}
