﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace NAVI
{
    /// <summary>
    /// LoginControl.xaml 的交互逻辑
    /// </summary>
    public partial class LoginControl : UserControl
    {
        private MainWindow _main;
        public LoginControl(MainWindow main)
        {
            InitializeComponent();
            _main = main;
        }
        /// <summary>
        /// 登录按钮点击事件
        /// </summary>
        private void Button_Click(object sender, RoutedEventArgs e)
        {
            // 验证用户名和密码
            if (string.IsNullOrWhiteSpace(userName.Text))
            {
                MessageBox.Show("请输入用户名！", "登录提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                userName.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(passWord.Password))
            {
                MessageBox.Show("请输入密码！", "登录提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                passWord.Focus();
                return;
            }

            // 简单的用户验证（实际项目中应该连接数据库或API验证）
            if (userName.Text == "admin" && passWord.Password == "123456")
            {
                // 登录成功，生成用户ID（这里使用当前时间戳模拟）
                string userId = DateTime.Now.ToString("yyyyMMddHHmm");
                _main.NavigateToMain(userId);
            }
            else
            {
                MessageBox.Show("用户名或密码错误！", "登录失败", MessageBoxButton.OK, MessageBoxImage.Error);
                passWord.Password = ""; // 清空密码
                userName.Focus();
            }
        }
    }
}
