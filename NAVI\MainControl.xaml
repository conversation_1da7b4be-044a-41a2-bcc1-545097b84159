<UserControl x:Class="NAVI.MainControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:NAVI"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="300"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        <!-- 左侧树形菜单 -->
        <Border Grid.Column="0" Background="#F5F5F5">
            <TreeView Name="MenuTree" SelectedItemChanged="MenuTree_SelectedItemChanged" Margin="8">
                <TreeViewItem Header="数据库">
                    <TreeViewItem Header="事业者管理数据" Tag="BusinessData"/>
                    <TreeViewItem Header="国保连数据" Tag="NationalData"/>
                    <TreeViewItem Header="匹配结果数据" Tag="MatchData"/>
                </TreeViewItem>
                <TreeViewItem Header="处理功能">
                    <TreeViewItem Header="事业者Excel导入" Tag="ExcelImport"/>
                    <TreeViewItem Header="事业者纸质资料导入" Tag="PaperImport"/>
                    <TreeViewItem Header="国保连CSV导入" Tag="CSVImport"/>
                    <TreeViewItem Header="数据匹配" Tag="DataMatch"/>
                    <TreeViewItem Header="数据保存" Tag="DataSave"/>
                </TreeViewItem>
                <TreeViewItem Header="财务状况"/>
                <TreeViewItem Header="输出功能">
                    <TreeViewItem Header="财务CSV输出" Tag="FinanceExport"/>
                    <TreeViewItem Header="补助金CSV输出" Tag="SubsidyExport"/>
                </TreeViewItem>
                <TreeViewItem Header="月度结转处理">
                    <TreeViewItem Header="本月处理状况" Tag="MonthStatus"/>
                </TreeViewItem>
                <TreeViewItem Header="主数据">
                    <TreeViewItem Header="事业者主数据" Tag="BusinessMaster"/>
                    <TreeViewItem Header="查查登记数据" Tag="RegisterData"/>
                    <TreeViewItem Header="服务代码数据" Tag="ServiceCodeData"/>
                    <TreeViewItem Header="职员主数据" Tag="StaffMaster"/>
                </TreeViewItem>
                <TreeViewItem Header="系统设置"/>
            </TreeView>
        </Border>
        <!-- 右侧内容区 -->
        <Border Grid.Column="1" Background="White" Margin="8,0,0,0">
            <ContentControl Name="MainContent"/>
        </Border>
    </Grid>
</UserControl>
