using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;

namespace NAVI.Models
{
    /// <summary>
    /// 国保联数据模型
    /// </summary>
    public class NationalData : INotifyPropertyChanged
    {
        private Dictionary<string, object> _data = new Dictionary<string, object>();

        /// <summary>
        /// 动态属性访问器
        /// </summary>
        public object this[string propertyName]
        {
            get
            {
                return _data.ContainsKey(propertyName) ? _data[propertyName] : null;
            }
            set
            {
                _data[propertyName] = value;
                OnPropertyChanged(propertyName);
            }
        }

        /// <summary>
        /// 获取所有属性名
        /// </summary>
        public IEnumerable<string> PropertyNames => _data.Keys;

        /// <summary>
        /// 获取所有数据
        /// </summary>
        public Dictionary<string, object> GetAllData()
        {
            return new Dictionary<string, object>(_data);
        }

        /// <summary>
        /// 设置所有数据
        /// </summary>
        public void SetAllData(Dictionary<string, object> data)
        {
            _data = new Dictionary<string, object>(data);
            OnPropertyChanged(string.Empty); // 通知所有属性变化
        }

        /// <summary>
        /// 检查是否包含属性
        /// </summary>
        public bool HasProperty(string propertyName)
        {
            return _data.ContainsKey(propertyName);
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 国保联数据服务类
    /// </summary>
    public static class NationalDataService
    {
        /// <summary>
        /// 从字典数据创建国保联数据列表
        /// </summary>
        public static List<NationalData> CreateFromDictionaries(List<Dictionary<string, object>> dictionaries)
        {
            var result = new List<NationalData>();
            
            foreach (var dict in dictionaries)
            {
                var nationalData = new NationalData();
                nationalData.SetAllData(dict);
                result.Add(nationalData);
            }
            
            return result;
        }

        /// <summary>
        /// 将国保联数据列表转换为字典列表
        /// </summary>
        public static List<Dictionary<string, object>> ConvertToDictionaries(List<NationalData> nationalDataList)
        {
            return nationalDataList.Select(nd => nd.GetAllData()).ToList();
        }

        /// <summary>
        /// 获取模拟数据（如果Excel文件不存在时使用）
        /// </summary>
        public static List<NationalData> GetSampleData()
        {
            var sampleDictionaries = new List<Dictionary<string, object>>
            {
                new Dictionary<string, object>
                {
                    ["序号"] = 1,
                    ["保险者番号"] = "131016",
                    ["被保险者番号"] = "1234567890",
                    ["氏名"] = "田中太郎",
                    ["生年月日"] = "1980/04/15",
                    ["性别"] = "男",
                    ["住所"] = "东京都新宿区西新宿1-1-1",
                    ["电话番号"] = "03-1234-5678",
                    ["认定年月日"] = "2023/04/01",
                    ["有效期限"] = "2024/03/31"
                },
                new Dictionary<string, object>
                {
                    ["序号"] = 2,
                    ["保险者番号"] = "131016",
                    ["被保险者番号"] = "2345678901",
                    ["氏名"] = "佐藤花子",
                    ["生年月日"] = "1975/08/22",
                    ["性别"] = "女",
                    ["住所"] = "东京都渋谷区渋谷2-2-2",
                    ["电话番号"] = "03-2345-6789",
                    ["认定年月日"] = "2023/05/15",
                    ["有效期限"] = "2024/05/14"
                },
                new Dictionary<string, object>
                {
                    ["序号"] = 3,
                    ["保险者番号"] = "131024",
                    ["被保险者番号"] = "3456789012",
                    ["氏名"] = "铃木一郎",
                    ["生年月日"] = "1965/12/03",
                    ["性别"] = "男",
                    ["住所"] = "东京都品川区大井1-3-3",
                    ["电话番号"] = "03-3456-7890",
                    ["认定年月日"] = "2023/06/01",
                    ["有效期限"] = "2024/05/31"
                }
            };

            return CreateFromDictionaries(sampleDictionaries);
        }
    }
}
