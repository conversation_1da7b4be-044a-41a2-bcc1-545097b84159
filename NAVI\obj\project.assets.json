{"version": 3, "targets": {".NETFramework,Version=v4.7.2": {"MaterialDesignColors/2.1.4": {"type": "package", "compile": {"lib/net462/MaterialDesignColors.dll": {}}, "runtime": {"lib/net462/MaterialDesignColors.dll": {}}}, "MaterialDesignThemes/4.9.0": {"type": "package", "dependencies": {"MaterialDesignColors": "2.1.4", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "compile": {"lib/net462/MaterialDesignThemes.Wpf.dll": {}}, "runtime": {"lib/net462/MaterialDesignThemes.Wpf.dll": {}}, "build": {"build/MaterialDesignThemes.targets": {}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"type": "package", "frameworkAssemblies": ["PresentationCore", "PresentationFramework", "System", "System.Core", "System.XML", "System.Xaml", "WindowsBase"], "compile": {"lib/net45/Microsoft.Xaml.Behaviors.dll": {}}, "runtime": {"lib/net45/Microsoft.Xaml.Behaviors.dll": {}}}}, ".NETFramework,Version=v4.7.2/win": {"MaterialDesignColors/2.1.4": {"type": "package", "compile": {"lib/net462/MaterialDesignColors.dll": {}}, "runtime": {"lib/net462/MaterialDesignColors.dll": {}}}, "MaterialDesignThemes/4.9.0": {"type": "package", "dependencies": {"MaterialDesignColors": "2.1.4", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "compile": {"lib/net462/MaterialDesignThemes.Wpf.dll": {}}, "runtime": {"lib/net462/MaterialDesignThemes.Wpf.dll": {}}, "build": {"build/MaterialDesignThemes.targets": {}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"type": "package", "frameworkAssemblies": ["PresentationCore", "PresentationFramework", "System", "System.Core", "System.XML", "System.Xaml", "WindowsBase"], "compile": {"lib/net45/Microsoft.Xaml.Behaviors.dll": {}}, "runtime": {"lib/net45/Microsoft.Xaml.Behaviors.dll": {}}}}, ".NETFramework,Version=v4.7.2/win-x64": {"MaterialDesignColors/2.1.4": {"type": "package", "compile": {"lib/net462/MaterialDesignColors.dll": {}}, "runtime": {"lib/net462/MaterialDesignColors.dll": {}}}, "MaterialDesignThemes/4.9.0": {"type": "package", "dependencies": {"MaterialDesignColors": "2.1.4", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "compile": {"lib/net462/MaterialDesignThemes.Wpf.dll": {}}, "runtime": {"lib/net462/MaterialDesignThemes.Wpf.dll": {}}, "build": {"build/MaterialDesignThemes.targets": {}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"type": "package", "frameworkAssemblies": ["PresentationCore", "PresentationFramework", "System", "System.Core", "System.XML", "System.Xaml", "WindowsBase"], "compile": {"lib/net45/Microsoft.Xaml.Behaviors.dll": {}}, "runtime": {"lib/net45/Microsoft.Xaml.Behaviors.dll": {}}}}, ".NETFramework,Version=v4.7.2/win-x86": {"MaterialDesignColors/2.1.4": {"type": "package", "compile": {"lib/net462/MaterialDesignColors.dll": {}}, "runtime": {"lib/net462/MaterialDesignColors.dll": {}}}, "MaterialDesignThemes/4.9.0": {"type": "package", "dependencies": {"MaterialDesignColors": "2.1.4", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "compile": {"lib/net462/MaterialDesignThemes.Wpf.dll": {}}, "runtime": {"lib/net462/MaterialDesignThemes.Wpf.dll": {}}, "build": {"build/MaterialDesignThemes.targets": {}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"type": "package", "frameworkAssemblies": ["PresentationCore", "PresentationFramework", "System", "System.Core", "System.XML", "System.Xaml", "WindowsBase"], "compile": {"lib/net45/Microsoft.Xaml.Behaviors.dll": {}}, "runtime": {"lib/net45/Microsoft.Xaml.Behaviors.dll": {}}}}}, "libraries": {"MaterialDesignColors/2.1.4": {"sha512": "C4Oy+qkjMoMPoZKyqYdCnIYtK8c0OSIHmNP73Vgc69NjiUG093xTkE7W/Ks54cTDS7fmWOtUHfwISTVTtb/YKg==", "type": "package", "path": "materialdesigncolors/2.1.4", "files": [".nupkg.metadata", ".signature.p7s", "images/MaterialDesignColors.Icon.png", "lib/net462/MaterialDesignColors.dll", "lib/net462/MaterialDesignColors.pdb", "lib/net6.0/MaterialDesignColors.dll", "lib/net6.0/MaterialDesignColors.pdb", "lib/net7.0/MaterialDesignColors.dll", "lib/net7.0/MaterialDesignColors.pdb", "lib/netcoreapp3.1/MaterialDesignColors.dll", "lib/netcoreapp3.1/MaterialDesignColors.pdb", "materialdesigncolors.2.1.4.nupkg.sha512", "materialdesigncolors.nuspec"]}, "MaterialDesignThemes/4.9.0": {"sha512": "Bp9Auw70j+9V7WsUMT4pc8ulVzfL0Eav/tyGgICDirxxhKJwhqtC/6PRkTUm+R1t9611xiDuk5pSUNdDV6vfOQ==", "type": "package", "path": "materialdesignthemes/4.9.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/MaterialDesignThemes.targets", "build/Resources/Roboto/Roboto-Black.ttf", "build/Resources/Roboto/Roboto-BlackItalic.ttf", "build/Resources/Roboto/Roboto-Bold.ttf", "build/Resources/Roboto/Roboto-BoldItalic.ttf", "build/Resources/Roboto/Roboto-Italic.ttf", "build/Resources/Roboto/Roboto-Light.ttf", "build/Resources/Roboto/Roboto-LightItalic.ttf", "build/Resources/Roboto/Roboto-Medium.ttf", "build/Resources/Roboto/Roboto-MediumItalic.ttf", "build/Resources/Roboto/Roboto-Regular.ttf", "build/Resources/Roboto/Roboto-Thin.ttf", "build/Resources/Roboto/Roboto-ThinItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Bold.ttf", "build/Resources/Roboto/RobotoCondensed-BoldItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Italic.ttf", "build/Resources/Roboto/RobotoCondensed-Light.ttf", "build/Resources/Roboto/RobotoCondensed-LightItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Regular.ttf", "images/MaterialDesignThemes.Icon.png", "lib/net462/MaterialDesignThemes.Wpf.dll", "lib/net462/MaterialDesignThemes.Wpf.pdb", "lib/net462/MaterialDesignThemes.Wpf.xml", "lib/net6.0/MaterialDesignThemes.Wpf.dll", "lib/net6.0/MaterialDesignThemes.Wpf.pdb", "lib/net6.0/MaterialDesignThemes.Wpf.xml", "lib/net7.0/MaterialDesignThemes.Wpf.dll", "lib/net7.0/MaterialDesignThemes.Wpf.pdb", "lib/net7.0/MaterialDesignThemes.Wpf.xml", "lib/netcoreapp3.1/MaterialDesignThemes.Wpf.dll", "lib/netcoreapp3.1/MaterialDesignThemes.Wpf.pdb", "lib/netcoreapp3.1/MaterialDesignThemes.Wpf.xml", "materialdesignthemes.4.9.0.nupkg.sha512", "materialdesignthemes.nuspec", "tools/VisualStudioToolsManifest.xml"]}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"sha512": "8PZKqw9QOcu42xk8puY4P1+EXHL9YGOR9b7qhaYx5cILHul456H073tj99vyPcCt0W0781T9RwHqkx507ZyUpQ==", "type": "package", "path": "microsoft.xaml.behaviors.wpf/1.1.39", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Design/Microsoft.Xaml.Behaviors.Design.dll", "lib/net45/Microsoft.Xaml.Behaviors.dll", "lib/net45/Microsoft.Xaml.Behaviors.pdb", "lib/net45/Microsoft.Xaml.Behaviors.xml", "lib/net5.0-windows7.0/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.pdb", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.xml", "lib/netcoreapp3.1/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.dll", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.pdb", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.xml", "microsoft.xaml.behaviors.wpf.1.1.39.nupkg.sha512", "microsoft.xaml.behaviors.wpf.nuspec", "tools/Install.ps1"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.7.2": ["MaterialDesignColors >= 2.1.4", "MaterialDesignThemes >= 4.9.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\songpl\\wpfProject\\NAVI\\NAVI\\NAVI\\NAVI.csproj", "projectName": "NAVI", "projectPath": "D:\\songpl\\wpfProject\\NAVI\\NAVI\\NAVI\\NAVI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\songpl\\wpfProject\\NAVI\\NAVI\\NAVI\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net472": {"projectReferences": {}}}}, "frameworks": {"net472": {"dependencies": {"MaterialDesignColors": {"target": "Package", "version": "[2.1.4, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}}}}, "runtimes": {"win": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}