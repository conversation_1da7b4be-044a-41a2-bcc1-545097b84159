<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- 颜色主题定义 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="#FF2196F3"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="#FF1976D2"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="#FFBBDEFB"/>
    <SolidColorBrush x:Key="AccentBrush" Color="#FF03DAC6"/>
    <SolidColorBrush x:Key="BackgroundBrush" Color="#FFF8F9FA"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFFFF"/>
    <SolidColorBrush x:Key="OnSurfaceBrush" Color="#FF212121"/>
    <SolidColorBrush x:Key="SecondaryBrush" Color="#FF757575"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="#FFF44336"/>
    <SolidColorBrush x:Key="SuccessBrush" Color="#FF4CAF50"/>
    <SolidColorBrush x:Key="WarningBrush" Color="#FFFF9800"/>
    <SolidColorBrush x:Key="BorderBrush" Color="#FFE0E0E0"/>
    <SolidColorBrush x:Key="HoverBrush" Color="#FFF0F8FF"/>
    <SolidColorBrush x:Key="SelectedBrush" Color="#FFE3F2FD"/>

    <!-- 字体定义 -->
    <FontFamily x:Key="PrimaryFont">Microsoft YaHei UI, Segoe UI, Arial</FontFamily>
    
    <!-- 基础按钮样式 -->
    <Style x:Key="BaseButtonStyle" TargetType="Button">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="MinHeight" Value="36"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="ButtonBorder"
                           Background="{TemplateBinding Background}" 
                           CornerRadius="4" 
                           Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" 
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 主要按钮样式 -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource BaseButtonStyle}">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#FF1565C0"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 次要按钮样式 -->
    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource BaseButtonStyle}">
        <Setter Property="Background" Value="{StaticResource SecondaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#FF616161"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#FF424242"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 危险按钮样式 -->
    <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource BaseButtonStyle}">
        <Setter Property="Background" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#FFD32F2F"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#FFC62828"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 文本框样式 -->
    <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="MinHeight" Value="36"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border x:Name="TextBoxBorder"
                           Background="{TemplateBinding Background}" 
                           BorderBrush="{TemplateBinding BorderBrush}" 
                           BorderThickness="{TemplateBinding BorderThickness}" 
                           CornerRadius="4">
                        <ScrollViewer x:Name="PART_ContentHost" 
                                     Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="TextBoxBorder" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter TargetName="TextBoxBorder" Property="BorderThickness" Value="2"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="TextBoxBorder" Property="BorderBrush" Value="{StaticResource PrimaryLightBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 标题文本样式 -->
    <Style x:Key="TitleTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{StaticResource OnSurfaceBrush}"/>
    </Style>

    <!-- 副标题文本样式 -->
    <Style x:Key="SubtitleTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{StaticResource OnSurfaceBrush}"/>
    </Style>

    <!-- 正文文本样式 -->
    <Style x:Key="BodyTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FontWeight" Value="Regular"/>
        <Setter Property="Foreground" Value="{StaticResource OnSurfaceBrush}"/>
    </Style>

    <!-- 卡片样式 -->
    <Style x:Key="CardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#40000000" 
                                 Direction="270" 
                                 ShadowDepth="2" 
                                 BlurRadius="8" 
                                 Opacity="0.1"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 分隔线样式 -->
    <Style x:Key="SeparatorStyle" TargetType="Rectangle">
        <Setter Property="Fill" Value="{StaticResource BorderBrush}"/>
        <Setter Property="Height" Value="1"/>
        <Setter Property="Margin" Value="0,8"/>
    </Style>

</ResourceDictionary>
