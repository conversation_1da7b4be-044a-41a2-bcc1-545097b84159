{"format": 1, "restore": {"D:\\songpl\\wpfProject\\NAVI\\NAVI\\NAVI\\NAVI.csproj": {}}, "projects": {"D:\\songpl\\wpfProject\\NAVI\\NAVI\\NAVI\\NAVI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\songpl\\wpfProject\\NAVI\\NAVI\\NAVI\\NAVI.csproj", "projectName": "NAVI", "projectPath": "D:\\songpl\\wpfProject\\NAVI\\NAVI\\NAVI\\NAVI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\songpl\\wpfProject\\NAVI\\NAVI\\NAVI\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net472": {"projectReferences": {}}}}, "frameworks": {"net472": {"dependencies": {"MaterialDesignColors": {"target": "Package", "version": "[2.1.4, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "NPOI": {"target": "Package", "version": "[2.6.2, )"}}}}, "runtimes": {"win": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}}